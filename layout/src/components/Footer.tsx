import { Separator } from "./ui/separator";
import { But<PERSON> } from "./ui/button";
import { Mail, Linkedin, Twitter, Instagram } from "lucide-react";

const footerLinks = {
  product: [
    { name: "Ferramentas", href: "#features" },
    { name: "Preços", href: "#pricing" },
    { name: "Integrações", href: "#integrations" },
    { name: "API", href: "#api" },
  ],
  company: [
    { name: "Sobre Nós", href: "#about" },
    { name: "Blog", href: "#blog" },
    { name: "Carreiras", href: "#careers" },
    { name: "<PERSON><PERSON><PERSON>", href: "#contact" },
  ],
  support: [
    { name: "Centro de Ajuda", href: "#help" },
    { name: "Documentação", href: "#docs" },
    { name: "Status", href: "#status" },
    { name: "Comunidade", href: "#community" },
  ],
  legal: [
    { name: "Privacidade", href: "#privacy" },
    { name: "<PERSON>rm<PERSON>", href: "#terms" },
    { name: "Cook<PERSON>", href: "#cookies" },
    { name: "LGPD", href: "#lgpd" },
  ],
};

export function Footer() {
  return (
    <footer className="bg-muted/30 pt-16 pb-8">
      <div className="container px-4 md:px-8">
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8 mb-12">
          {/* Logo and description */}
          <div className="col-span-2 space-y-4">
            <h3 className="text-2xl font-bold text-primary">uTulz</h3>
            <p className="text-muted-foreground max-w-md">
              Plataforma SaaS que acelera o trabalho de agências e profissionais 
              de publicidade com ferramentas de IA especializadas.
            </p>
            <div className="flex space-x-2">
              <Button variant="outline" size="icon">
                <Linkedin className="w-4 h-4" />
              </Button>
              <Button variant="outline" size="icon">
                <Twitter className="w-4 h-4" />
              </Button>
              <Button variant="outline" size="icon">
                <Instagram className="w-4 h-4" />
              </Button>
              <Button variant="outline" size="icon">
                <Mail className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Product */}
          <div className="space-y-4">
            <h4 className="font-medium">Produto</h4>
            <ul className="space-y-2">
              {footerLinks.product.map((link, index) => (
                <li key={index}>
                  <a 
                    href={link.href} 
                    className="text-muted-foreground hover:text-foreground transition-colors text-sm"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Company */}
          <div className="space-y-4">
            <h4 className="font-medium">Empresa</h4>
            <ul className="space-y-2">
              {footerLinks.company.map((link, index) => (
                <li key={index}>
                  <a 
                    href={link.href} 
                    className="text-muted-foreground hover:text-foreground transition-colors text-sm"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Support */}
          <div className="space-y-4">
            <h4 className="font-medium">Suporte</h4>
            <ul className="space-y-2">
              {footerLinks.support.map((link, index) => (
                <li key={index}>
                  <a 
                    href={link.href} 
                    className="text-muted-foreground hover:text-foreground transition-colors text-sm"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Legal */}
          <div className="space-y-4">
            <h4 className="font-medium">Legal</h4>
            <ul className="space-y-2">
              {footerLinks.legal.map((link, index) => (
                <li key={index}>
                  <a 
                    href={link.href} 
                    className="text-muted-foreground hover:text-foreground transition-colors text-sm"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>
        </div>

        <Separator />
        
        <div className="flex flex-col md:flex-row justify-between items-center pt-8">
          <p className="text-muted-foreground text-sm">
            © 2025 uTulz. Todos os direitos reservados.
          </p>
          <div className="flex items-center space-x-4 mt-4 md:mt-0">
            <p className="text-muted-foreground text-sm">
              Feito com ❤️ no Brasil
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}