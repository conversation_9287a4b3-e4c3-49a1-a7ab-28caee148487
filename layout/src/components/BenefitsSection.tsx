import { Card, CardContent } from "./ui/card";
import { Badge } from "./ui/badge";
import { Button } from "./ui/button";
import { 
  Clock, 
  Trophy, 
  Users, 
  DollarSign, 
  Shield, 
  Rocket,
  CheckCircle,
  ArrowRight
} from "lucide-react";
import { ImageWithFallback } from "./figma/ImageWithFallback";

const benefits = [
  {
    icon: Clock,
    title: "Economize 60% do Tempo",
    description: "Automatize tarefas repetitivas e foque no que realmente importa: estratégia e criatividade.",
  },
  {
    icon: Trophy,
    title: "Qualidade Consistente",
    description: "Garanta padrão profissional em todas as entregas com IA treinada nas melhores práticas.",
  },
  {
    icon: DollarSign,
    title: "Aumente sua Margem",
    description: "Reduza custos operacionais e acelere entregas para aumentar a lucratividade dos projetos.",
  },
  {
    icon: Shield,
    title: "Dados Seguros",
    description: "Plataforma com segurança enterprise e conformidade com LGPD para proteger informações sensíveis.",
  }
];

const testimonials = [
  {
    name: "<PERSON>",
    role: "Diretora Criativa",
    company: "Agência Inovação",
    content: "Com a uTulz, conseguimos triplicar nossa capacidade de entrega sem aumentar a equipe. A qualidade se mant��m alta e o time pode focar em estratégia.",
    image: "https://images.unsplash.com/photo-1709281847802-9aef10b6d4bf?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxkaWdpdGFsJTIwbWFya2V0aW5nJTIwYWdlbmN5JTIwd29ya3NwYWNlfGVufDF8fHx8MTc1NjgyMjgxNXww&ixlib=rb-4.1.0&q=80&w=1080&utm_source=figma&utm_medium=referral"
  },
  {
    name: "Carlos Mendes",
    role: "Freelancer",
    company: "Marketing Digital",
    content: "Como freelancer, a uTulz me permite competir com grandes agências. Consigo entregar projetos complexos em muito menos tempo.",
    image: "https://images.unsplash.com/photo-1752650736352-9fa50eb3055f?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxjcmVhdGl2ZSUyMGFkdmVydGlzaW5nJTIwdGVhbXxlbnwxfHx8fDE3NTY4MjI4MTV8MA&ixlib=rb-4.1.0&q=80&w=1080&utm_source=figma&utm_medium=referral"
  }
];

export function BenefitsSection() {
  return (
    <section id="benefits" className="py-20 md:py-32 bg-muted/30">
      <div className="container px-4 md:px-8">
        {/* Header */}
        <div className="text-center space-y-4 mb-16">
          <Badge variant="secondary" className="w-fit mx-auto">
            <Rocket className="w-3 h-3 mr-2" />
            Vantagens Competitivas
          </Badge>
          
          <h2 className="text-3xl md:text-4xl lg:text-5xl tracking-tight">
            Por que escolher a{" "}
            <span className="text-primary">uTulz</span>?
          </h2>
          
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Transforme sua operação e se destaque no mercado com ferramentas 
            que realmente fazem a diferença no seu resultado.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 items-center mb-20">
          {/* Left - Benefits */}
          <div className="space-y-6">
            {benefits.map((benefit, index) => (
              <div key={index} className="flex items-start space-x-4 group">
                <div className="flex-shrink-0 p-3 rounded-xl bg-card shadow-sm group-hover:shadow-md transition-shadow">
                  <benefit.icon className="w-6 h-6 text-primary" />
                </div>
                <div className="space-y-2">
                  <h3 className="text-xl">{benefit.title}</h3>
                  <p className="text-muted-foreground">{benefit.description}</p>
                </div>
              </div>
            ))}
            
            <div className="pt-6">
              <Button size="lg" className="group">
                Ver Todos os Benefícios
                <ArrowRight className="w-4 h-4 ml-2 transition-transform group-hover:translate-x-1" />
              </Button>
            </div>
          </div>

          {/* Right - Image */}
          <div className="relative">
            <Card className="overflow-hidden">
              <ImageWithFallback
                src="https://images.unsplash.com/photo-1739298061766-e2751d92e9db?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxwcm9mZXNzaW9uYWwlMjBjb2xsYWJvcmF0aW9uJTIwbWVldGluZ3xlbnwxfHx8fDE3NTY4MjI4MTV8MA&ixlib=rb-4.1.0&q=80&w=1080&utm_source=figma&utm_medium=referral"
                alt="Equipe colaborando com uTulz"
                className="w-full h-auto"
              />
            </Card>

            {/* Success metrics overlay */}
            <Card className="absolute top-4 right-4 p-4 bg-card/95 backdrop-blur-sm">
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="text-sm">+500 projetos entregues</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="text-sm">98% satisfação</span>
                </div>
              </div>
            </Card>
          </div>
        </div>

        {/* Testimonials */}
        <div className="space-y-8">
          <div className="text-center">
            <h3 className="text-2xl md:text-3xl">O que nossos clientes dizem</h3>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="p-6">
                <CardContent className="space-y-4 p-0">
                  <p className="text-muted-foreground italic">"{testimonial.content}"</p>
                  
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 rounded-full overflow-hidden">
                      <ImageWithFallback
                        src={testimonial.image}
                        alt={testimonial.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div>
                      <div className="font-medium">{testimonial.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {testimonial.role}, {testimonial.company}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}