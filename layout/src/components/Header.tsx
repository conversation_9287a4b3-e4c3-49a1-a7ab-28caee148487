import { But<PERSON> } from "./ui/button";
import { <PERSON>u, X } from "lucide-react";
import { useState } from "react";
import { ThemeToggle } from "./ThemeToggle";

interface HeaderProps {
  onNavigateToTools?: () => void;
}

export function Header({ onNavigateToTools }: HeaderProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center justify-between px-4 md:px-8">
        {/* Logo */}
        <div className="flex items-center">
          <h1 className="text-2xl font-bold text-primary">uTulz</h1>
        </div>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center space-x-8">
          <button 
            onClick={onNavigateToTools}
            className="text-foreground hover:text-primary transition-colors"
          >
            Ferramentas
          </button>
          <a href="#benefits" className="text-foreground hover:text-primary transition-colors">
            Benefícios
          </a>
          <a href="#pricing" className="text-foreground hover:text-primary transition-colors">
            Preços
          </a>
          <a href="#contact" className="text-foreground hover:text-primary transition-colors">
            Contato
          </a>
        </nav>

        {/* CTA Buttons */}
        <div className="hidden md:flex items-center space-x-4">
          <ThemeToggle />
          <Button variant="outline">Entrar</Button>
          <Button>Começar Grátis</Button>
        </div>

        {/* Mobile Menu Button */}
        <button 
          className="md:hidden"
          onClick={toggleMenu}
          aria-label="Menu"
        >
          {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
        </button>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="md:hidden border-t bg-background">
          <nav className="container py-4 px-4 space-y-4">
            <button 
              className="block text-foreground hover:text-primary transition-colors w-full text-left"
              onClick={() => {
                setIsMenuOpen(false);
                onNavigateToTools?.();
              }}
            >
              Ferramentas
            </button>
            <a 
              href="#benefits" 
              className="block text-foreground hover:text-primary transition-colors"
              onClick={() => setIsMenuOpen(false)}
            >
              Benefícios
            </a>
            <a 
              href="#pricing" 
              className="block text-foreground hover:text-primary transition-colors"
              onClick={() => setIsMenuOpen(false)}
            >
              Preços
            </a>
            <a 
              href="#contact" 
              className="block text-foreground hover:text-primary transition-colors"
              onClick={() => setIsMenuOpen(false)}
            >
              Contato
            </a>
            <div className="flex flex-col space-y-2 pt-4">
              <div className="flex justify-center pb-2">
                <ThemeToggle />
              </div>
              <Button variant="outline" className="w-full">Entrar</Button>
              <Button className="w-full">Começar Grátis</Button>
            </div>
          </nav>
        </div>
      )}
    </header>
  );
}