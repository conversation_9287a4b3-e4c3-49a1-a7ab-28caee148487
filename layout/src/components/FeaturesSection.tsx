import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "./ui/card";
import { Badge } from "./ui/badge";
import { Button } from "./ui/button";
import { 
  Brain, 
  PenTool, 
  BarChart3, 
  MessageSquare, 
  Palette, 
  Calendar,
  ArrowRight,
  Sparkles
} from "lucide-react";

const features = [
  {
    icon: PenTool,
    title: "Criação de Conteúdo",
    description: "IA especializada em criar copy, headlines e textos publicitários otimizados para diferentes plataformas e públicos.",
    badge: "Copywriting",
    color: "text-blue-500"
  },
  {
    icon: Palette,
    title: "Design Inteligente",
    description: "Geração automatizada de layouts, paletas de cores e elementos visuais alinhados com a identidade da marca.",
    badge: "Design",
    color: "text-purple-500"
  },
  {
    icon: BarChart3,
    title: "Análise de Performance",
    description: "Monitoramento e otimização contínua de campanhas com insights acionáveis baseados em dados reais.",
    badge: "Analytics",
    color: "text-green-500"
  },
  {
    icon: MessageSquare,
    title: "Gestão de Social Media",
    description: "Planejamento, criação e agendamento automático de conteúdo para redes sociais com tom de voz consistente.",
    badge: "Social Media",
    color: "text-pink-500"
  },
  {
    icon: Brain,
    title: "Estratégia de Campanha",
    description: "Desenvolvimento de estratégias publicitárias baseadas em dados de mercado e comportamento do consumidor.",
    badge: "Estratégia",
    color: "text-orange-500"
  },
  {
    icon: Calendar,
    title: "Planejamento de Mídia",
    description: "Otimização de mix de mídia e alocação de budget com base em performance histórica e previsões de mercado.",
    badge: "Mídia",
    color: "text-cyan-500"
  }
];

interface FeaturesSectionProps {
  onNavigateToTools?: () => void;
}

export function FeaturesSection({ onNavigateToTools }: FeaturesSectionProps) {
  return (
    <section id="features" className="py-20 md:py-32">
      <div className="container px-4 md:px-8">
        {/* Header */}
        <div className="text-center space-y-4 mb-16">
          <Badge variant="secondary" className="w-fit mx-auto">
            <Sparkles className="w-3 h-3 mr-2" />
            Ferramentas Especializadas
          </Badge>
          
          <h2 className="text-3xl md:text-4xl lg:text-5xl tracking-tight">
            Agentes de IA para cada área da{" "}
            <span className="text-primary">publicidade</span>
          </h2>
          
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Cada ferramenta foi desenvolvida com conhecimento especializado para resolver 
            problemas específicos do seu dia-a-dia, garantindo resultados profissionais.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {features.map((feature, index) => (
            <Card key={index} className="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
              <CardHeader className="space-y-4">
                <div className="flex items-start justify-between">
                  <div className={`p-3 rounded-xl bg-muted ${feature.color}`}>
                    <feature.icon className="w-6 h-6" />
                  </div>
                  <Badge variant="outline" className="text-xs">
                    {feature.badge}
                  </Badge>
                </div>
                
                <CardTitle className="text-xl">{feature.title}</CardTitle>
              </CardHeader>
              
              <CardContent className="space-y-4">
                <p className="text-muted-foreground">{feature.description}</p>
                
                <Button variant="ghost" className="group/btn p-0 h-auto">
                  Saiba mais
                  <ArrowRight className="w-4 h-4 ml-2 transition-transform group-hover/btn:translate-x-1" />
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* CTA */}
        <div className="text-center">
          <Button size="lg" onClick={onNavigateToTools}>
            Explorar Todas as Ferramentas
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        </div>
      </div>
    </section>
  );
}