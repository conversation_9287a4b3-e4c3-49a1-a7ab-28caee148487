import { But<PERSON> } from "./ui/button";
import { Card } from "./ui/card";
import { Badge } from "./ui/badge";
import { ArrowRight, Play, Zap, Target, TrendingUp } from "lucide-react";
import { ImageWithFallback } from "./figma/ImageWithFallback";

interface HeroSectionProps {
  onNavigateToTools?: () => void;
}

export function HeroSection({ onNavigateToTools }: HeroSectionProps) {
  return (
    <section className="relative py-20 md:py-32 overflow-hidden">
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-accent/10" />
      
      <div className="container relative px-4 md:px-8">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Content */}
          <div className="space-y-8">
            <Badge variant="secondary" className="w-fit">
              <Zap className="w-3 h-3 mr-2" />
              Plataforma SaaS para Agências
            </Badge>
            
            <div className="space-y-6">
              <h1 className="text-4xl md:text-5xl lg:text-6xl tracking-tight">
                Acelere suas{" "}
                <span className="text-primary">entregas criativas</span>{" "}
                com IA especializada
              </h1>
              
              <p className="text-xl text-muted-foreground max-w-lg">
                A uTulz oferece ferramentas de IA especializadas para agências e profissionais 
                de publicidade, automatizando tarefas críticas e garantindo entregas mais 
                consistentes e de qualidade.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <Button size="lg" className="group">
                Começar Grátis
                <ArrowRight className="w-4 h-4 ml-2 transition-transform group-hover:translate-x-1" />
              </Button>
              <Button 
                size="lg" 
                variant="outline" 
                className="group"
                onClick={onNavigateToTools}
              >
                <Play className="w-4 h-4 mr-2" />
                Ver Ferramentas
              </Button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-6 pt-8 border-t">
              <div className="text-center">
                <div className="text-2xl">+85%</div>
                <div className="text-sm text-muted-foreground">Produtividade</div>
              </div>
              <div className="text-center">
                <div className="text-2xl">-60%</div>
                <div className="text-sm text-muted-foreground">Tempo de Entrega</div>
              </div>
              <div className="text-center">
                <div className="text-2xl">10x</div>
                <div className="text-sm text-muted-foreground">Mais Consistente</div>
              </div>
            </div>
          </div>

          {/* Right Content - Dashboard Preview */}
          <div className="relative">
            <Card className="p-2 bg-gradient-to-br from-card to-muted shadow-2xl">
              <ImageWithFallback
                src="https://images.unsplash.com/photo-1731846584223-81977e156b2c?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxhaSUyMHRlY2hub2xvZ3klMjBkYXNoYm9hcmR8ZW58MXx8fHwxNzU2ODIyODE1fDA&ixlib=rb-4.1.0&q=80&w=1080&utm_source=figma&utm_medium=referral"
                alt="uTulz Dashboard Preview"
                className="w-full h-auto rounded-lg"
              />
            </Card>

            {/* Floating elements */}
            <Card className="absolute -top-4 -left-4 p-4 bg-card shadow-lg w-fit">
              <div className="flex items-center space-x-2">
                <Target className="w-5 h-5 text-primary" />
                <span className="text-sm">+95% Precisão</span>
              </div>
            </Card>

            <Card className="absolute -bottom-4 -right-4 p-4 bg-card shadow-lg w-fit">
              <div className="flex items-center space-x-2">
                <TrendingUp className="w-5 h-5 text-green-500" />
                <span className="text-sm">ROI +200%</span>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
}