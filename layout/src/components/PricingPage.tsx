import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "./ui/card";
import { Badge } from "./ui/badge";
import { Button } from "./ui/button";
import { Separator } from "./ui/separator";
import { 
  Check, 
  ArrowLeft, 
  Star, 
  Users, 
  Building2, 
  Rocket,
  Zap,
  Shield,
  Headphones,
  Clock,
  Infinity
} from "lucide-react";

const plans = [
  {
    name: "Starter",
    description: "Perfeito para freelancers e pequenas agências",
    price: "R$ 97",
    period: "/mês",
    yearlyPrice: "R$ 970",
    yearlyPeriod: "/ano",
    savings: "2 meses grátis",
    icon: Rocket,
    color: "text-blue-500",
    popular: false,
    features: [
      "5 ferramentas MVP incluídas",
      "50 gerações por mês",
      "Suporte via chat",
      "Integração com Google Workspace",
      "Histórico de 30 dias",
      "1 usuário"
    ],
    tools: [
      "Briefing inicial",
      "Plano de mídia",
      "Conceito criativo",
      "Produção visual",
      "Publicação/Acompanhamento"
    ]
  },
  {
    name: "Professional",
    description: "Ideal para agências médias e times criativos",
    price: "R$ 297",
    period: "/mês",
    yearlyPrice: "R$ 2.970",
    yearlyPeriod: "/ano",
    savings: "2 meses grátis",
    icon: Building2,
    color: "text-purple-500",
    popular: true,
    features: [
      "Todas as 45+ ferramentas",
      "500 gerações por mês",
      "Suporte prioritário",
      "Integração completa (Google, Adobe, etc)",
      "Histórico ilimitado",
      "Até 10 usuários",
      "Relatórios avançados",
      "Templates personalizados",
      "API access"
    ],
    tools: [
      "Todas as ferramentas MVP",
      "Ferramentas de planejamento",
      "Social Media completo",
      "BI/Monitoramento",
      "Ferramentas extras"
    ]
  },
  {
    name: "Enterprise",
    description: "Para grandes agências e holdings",
    price: "Personalizado",
    period: "",
    yearlyPrice: "Sob consulta",
    yearlyPeriod: "",
    savings: "Volume discount",
    icon: Users,
    color: "text-green-500",
    popular: false,
    features: [
      "Todas as ferramentas + personalizações",
      "Gerações ilimitadas",
      "Suporte dedicado 24/7",
      "Integrações customizadas",
      "Histórico e backup ilimitados",
      "Usuários ilimitados",
      "Dashboard executivo",
      "Treinamento presencial",
      "SLA garantido",
      "White-label disponível"
    ],
    tools: [
      "Todas as ferramentas",
      "Ferramentas personalizadas",
      "Integração com sistemas legados",
      "Desenvolvimento sob demanda"
    ]
  }
];

const faqs = [
  {
    question: "Posso cancelar a qualquer momento?",
    answer: "Sim, você pode cancelar sua assinatura a qualquer momento. Não há taxas de cancelamento e você continuará tendo acesso até o final do período pago."
  },
  {
    question: "As ferramentas funcionam em português?",
    answer: "Sim, todas as nossas ferramentas foram treinadas e otimizadas para o mercado brasileiro, incluindo linguagem, cultura e regulamentações locais."
  },
  {
    question: "Preciso de treinamento para usar?",
    answer: "Nossas ferramentas são intuitivas, mas oferecemos onboarding gratuito para todos os planos e treinamento presencial para clientes Enterprise."
  },
  {
    question: "Os dados da minha agência ficam seguros?",
    answer: "Sim, utilizamos criptografia de ponta a ponta e seguimos todas as regulamentações de proteção de dados. Seus dados nunca são compartilhados ou usados para treinar nossos modelos."
  },
  {
    question: "Posso integrar com ferramentas que já uso?",
    answer: "Sim, oferecemos integrações nativas com Google Workspace, Adobe Creative Suite, Meta Business, Google Ads e muitas outras ferramentas populares."
  },
  {
    question: "Há limite de projetos ou campanhas?",
    answer: "Não há limite de projetos. O limite é apenas na quantidade de gerações de IA por mês, que varia conforme o plano escolhido."
  }
];

interface PricingPageProps {
  onBackToHome: () => void;
}

export function PricingPage({ onBackToHome }: PricingPageProps) {
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="sticky top-0 z-50 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container px-4 md:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={onBackToHome}
                className="flex items-center space-x-2"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>Voltar</span>
              </Button>
              <div>
                <h1 className="text-2xl font-bold">Preços</h1>
                <p className="text-muted-foreground">
                  Escolha o plano ideal para sua agência
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="container px-4 md:px-8 py-8">
        {/* Hero Section */}
        <div className="text-center space-y-4 mb-16">
          <Badge variant="secondary" className="w-fit mx-auto">
            <Zap className="w-3 h-3 mr-2" />
            Planos Flexíveis
          </Badge>
          
          <h2 className="text-3xl md:text-4xl lg:text-5xl tracking-tight">
            Acelere sua agência com{" "}
            <span className="text-primary">IA especializada</span>
          </h2>
          
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Ferramentas de IA desenvolvidas especificamente para publicidade. 
            Comece grátis e escale conforme sua agência cresce.
          </p>
        </div>

        {/* Pricing Cards */}
        <div className="grid lg:grid-cols-3 gap-8 mb-20">
          {plans.map((plan, index) => (
            <Card 
              key={index} 
              className={`relative transition-all hover:shadow-lg ${
                plan.popular ? 'ring-2 ring-primary/20 scale-105' : ''
              }`}
            >
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-primary text-primary-foreground">
                    <Star className="w-3 h-3 mr-1" />
                    Mais Popular
                  </Badge>
                </div>
              )}
              
              <CardHeader className="text-center space-y-4">
                <div className={`p-3 rounded-xl bg-muted ${plan.color} w-fit mx-auto`}>
                  <plan.icon className="w-6 h-6" />
                </div>
                
                <div>
                  <CardTitle className="text-2xl">{plan.name}</CardTitle>
                  <p className="text-muted-foreground mt-2">{plan.description}</p>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-baseline justify-center space-x-1">
                    <span className="text-3xl font-bold">{plan.price}</span>
                    <span className="text-muted-foreground">{plan.period}</span>
                  </div>
                  {plan.yearlyPrice && (
                    <div className="text-sm text-muted-foreground">
                      {plan.yearlyPrice}{plan.yearlyPeriod} • {plan.savings}
                    </div>
                  )}
                </div>
                
                <Button 
                  className={`w-full ${plan.popular ? '' : 'variant-outline'}`}
                  variant={plan.popular ? 'default' : 'outline'}
                >
                  {plan.name === 'Enterprise' ? 'Falar com Vendas' : 'Começar Agora'}
                </Button>
              </CardHeader>
              
              <CardContent className="space-y-6">
                <Separator />
                
                <div>
                  <h4 className="font-semibold mb-3 flex items-center">
                    <Check className="w-4 h-4 mr-2 text-green-500" />
                    Recursos Incluídos
                  </h4>
                  <ul className="space-y-2">
                    {plan.features.map((feature, idx) => (
                      <li key={idx} className="flex items-start space-x-2 text-sm">
                        <Check className="w-4 h-4 text-green-500 flex-shrink-0 mt-0.5" />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                
                <Separator />
                
                <div>
                  <h4 className="font-semibold mb-3">Ferramentas Disponíveis</h4>
                  <ul className="space-y-1">
                    {plan.tools.map((tool, idx) => (
                      <li key={idx} className="text-sm text-muted-foreground">
                        • {tool}
                      </li>
                    ))}
                  </ul>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Additional Benefits */}
        <div className="grid md:grid-cols-4 gap-6 mb-20">
          <div className="text-center space-y-3">
            <div className="p-3 rounded-xl bg-muted text-blue-500 w-fit mx-auto">
              <Shield className="w-6 h-6" />
            </div>
            <h3 className="font-semibold">Dados Seguros</h3>
            <p className="text-sm text-muted-foreground">
              Criptografia de ponta a ponta e compliance total com LGPD
            </p>
          </div>
          
          <div className="text-center space-y-3">
            <div className="p-3 rounded-xl bg-muted text-green-500 w-fit mx-auto">
              <Headphones className="w-6 h-6" />
            </div>
            <h3 className="font-semibold">Suporte Especializado</h3>
            <p className="text-sm text-muted-foreground">
              Time de especialistas em publicidade para te ajudar
            </p>
          </div>
          
          <div className="text-center space-y-3">
            <div className="p-3 rounded-xl bg-muted text-purple-500 w-fit mx-auto">
              <Clock className="w-6 h-6" />
            </div>
            <h3 className="font-semibold">Economia de Tempo</h3>
            <p className="text-sm text-muted-foreground">
              Reduza em até 80% o tempo gasto em tarefas repetitivas
            </p>
          </div>
          
          <div className="text-center space-y-3">
            <div className="p-3 rounded-xl bg-muted text-orange-500 w-fit mx-auto">
              <Infinity className="w-6 h-6" />
            </div>
            <h3 className="font-semibold">Sempre Atualizado</h3>
            <p className="text-sm text-muted-foreground">
              Novas ferramentas e recursos adicionados mensalmente
            </p>
          </div>
        </div>

        {/* FAQ Section */}
        <div className="max-w-3xl mx-auto">
          <div className="text-center space-y-4 mb-12">
            <h2 className="text-3xl font-bold">Perguntas Frequentes</h2>
            <p className="text-muted-foreground">
              Tire suas dúvidas sobre os planos e funcionalidades da uTulz
            </p>
          </div>
          
          <div className="space-y-6">
            {faqs.map((faq, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle className="text-lg">{faq.question}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">{faq.answer}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Final CTA */}
        <div className="text-center mt-20 p-8 rounded-2xl bg-muted">
          <h3 className="text-2xl font-bold mb-4">
            Pronto para acelerar sua agência?
          </h3>
          <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
            Junte-se a centenas de agências que já estão usando IA para criar campanhas 
            mais eficazes e entregar resultados excepcionais para seus clientes.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg">
              Começar Teste Grátis
            </Button>
            <Button size="lg" variant="outline">
              Agendar Demo
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}