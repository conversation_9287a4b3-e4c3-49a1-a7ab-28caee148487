import { But<PERSON> } from "./ui/button";
import { Badge } from "./ui/badge";
import { Separator } from "./ui/separator";
import { 
  Home, 
  Megaphone, 
  BarChart3, 
  Users, 
  Settings, 
  Plus,
  Brain,
  Palette,
  PenTool,
  MessageSquare,
  Calendar,
  ChevronRight
} from "lucide-react";

const navigationItems = [
  { icon: Home, label: "Dashboard", href: "#dashboard", active: false },
  { icon: Megaphone, label: "Campanhas", href: "#campaigns", active: true, count: 12 },
  { icon: BarChart3, label: "Analytics", href: "#analytics", active: false },
  { icon: Users, label: "Clientes", href: "#clients", active: false },
];

const toolsItems = [
  { icon: PenTool, label: "Copy Generator", href: "#copy-generator" },
  { icon: Palette, label: "Design AI", href: "#design-ai" },
  { icon: Brain, label: "Estratégia", href: "#strategy" },
  { icon: MessageSquare, label: "Social Media", href: "#social-media" },
  { icon: Calendar, label: "Planejamento", href: "#planning" },
];

interface SidebarProps {
  onNavigate?: (page: string) => void;
}

export function Sidebar({ onNavigate }: SidebarProps) {
  return (
    <div className="w-64 h-screen bg-sidebar border-r border-sidebar-border flex flex-col">
      {/* Logo */}
      <div className="p-6">
        <h1 className="text-2xl font-bold text-sidebar-primary">uTulz</h1>
        <p className="text-sm text-sidebar-foreground/60">Plataforma de IA</p>
      </div>

      {/* Create Campaign Button */}
      <div className="px-4 mb-6">
        <Button className="w-full" size="sm">
          <Plus className="w-4 h-4 mr-2" />
          Nova Campanha
        </Button>
      </div>

      {/* Navigation */}
      <div className="flex-1 px-4 space-y-6 overflow-y-auto">
        {/* Main Navigation */}
        <div className="space-y-1">
          <h3 className="text-xs uppercase tracking-wider text-sidebar-foreground/60 mb-3">
            Principal
          </h3>
          {navigationItems.map((item, index) => (
            <button
              key={index}
              className={`w-full flex items-center justify-between px-3 py-2 rounded-lg text-sm transition-colors ${
                item.active 
                  ? 'bg-sidebar-accent text-sidebar-accent-foreground' 
                  : 'text-sidebar-foreground hover:bg-sidebar-accent/50'
              }`}
              onClick={() => onNavigate && onNavigate(item.href.replace('#', ''))}
            >
              <div className="flex items-center space-x-3">
                <item.icon className="w-4 h-4" />
                <span>{item.label}</span>
              </div>
              {item.count && (
                <Badge variant="secondary" className="text-xs">
                  {item.count}
                </Badge>
              )}
            </button>
          ))}
        </div>

        <Separator />

        {/* AI Tools */}
        <div className="space-y-1">
          <h3 className="text-xs uppercase tracking-wider text-sidebar-foreground/60 mb-3">
            Ferramentas IA
          </h3>
          {toolsItems.map((item, index) => (
            <button
              key={index}
              className="w-full flex items-center justify-between px-3 py-2 rounded-lg text-sm text-sidebar-foreground hover:bg-sidebar-accent/50 transition-colors group"
              onClick={() => onNavigate && onNavigate(item.href.replace('#', ''))}
            >
              <div className="flex items-center space-x-3">
                <item.icon className="w-4 h-4" />
                <span>{item.label}</span>
              </div>
              <ChevronRight className="w-3 h-3 opacity-0 group-hover:opacity-100 transition-opacity" />
            </button>
          ))}
        </div>
      </div>

      {/* Bottom Section */}
      <div className="p-4 border-t border-sidebar-border">
        <button className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-sm text-sidebar-foreground hover:bg-sidebar-accent/50 transition-colors">
          <Settings className="w-4 h-4" />
          <span>Configurações</span>
        </button>
      </div>
    </div>
  );
}