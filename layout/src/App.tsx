import { useState } from "react";
import { Header } from "./components/Header";
import { HeroSection } from "./components/HeroSection";
import { FeaturesSection } from "./components/FeaturesSection";
import { BenefitsSection } from "./components/BenefitsSection";
import { CTASection } from "./components/CTASection";
import { Footer } from "./components/Footer";
import { ToolsPage } from "./components/ToolsPage";
import { PricingPage } from "./components/PricingPage";

export default function App() {
  const [currentPage, setCurrentPage] = useState<'home' | 'tools' | 'pricing'>('home');

  if (currentPage === 'tools') {
    return (
      <div className="dark min-h-screen bg-background">
        <ToolsPage onBackToHome={() => setCurrentPage('home')} />
      </div>
    );
  }

  if (currentPage === 'pricing') {
    return (
      <div className="dark min-h-screen bg-background">
        <PricingPage onBackToHome={() => setCurrentPage('home')} />
      </div>
    );
  }

  return (
    <div className="dark min-h-screen bg-background">
      <Header 
        onNavigateToTools={() => setCurrentPage('tools')}
        onNavigateToPricing={() => setCurrentPage('pricing')} 
      />
      <main>
        <HeroSection onNavigateToTools={() => setCurrentPage('tools')} />
        <FeaturesSection onNavigateToTools={() => setCurrentPage('tools')} />
        <BenefitsSection />
        <CTASection onNavigateToPricing={() => setCurrentPage('pricing')} />
      </main>
      <Footer />
    </div>
  );
}