import { BenefitsSection } from "@/components/composite/BenefitsSection";
import { CTASection } from "@/components/composite/CTASection";
import { FeaturesSection } from "@/components/composite/FeaturesSection";
import { Hero } from "@/components/composite/Hero";
import { ToolsCarousel } from "@/components/composite/ToolsCarousel";
import { ValueGrid } from "@/components/composite/ValueGrid";

export const metadata = { title: "uTulz — Suas ferramentas para campanhas" };

export default function HomePage() {
  const handleNavigateToTools = () => {
    // Scroll to tools section or navigate to tools page
    const toolsSection = document.getElementById('ferramentas');
    if (toolsSection) {
      toolsSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleNavigateToPricing = () => {
    // Navigate to pricing page or scroll to pricing section
    window.location.href = '/preco';
  };

  return (
    <>
      <Hero onNavigateToTools={handleNavigateToTools} />
      <FeaturesSection onNavigateToTools={handleNavigateToTools} />
      <BenefitsSection />
      <section id="ferramentas">
        <ValueGrid />
        <ToolsCarousel />
      </section>
      <CTASection onNavigateToPricing={handleNavigateToPricing} />
      <section id="testar" className="bg-background py-16">
        <div className="mx-auto w-full max-w-3xl px-4 text-center">
          <h2 className="text-2xl font-semibold text-foreground">Pronto para testar</h2>
          <p className="mt-2 text-muted-foreground">Crie uma campanha de exemplo e veja a mágica acontecer.</p>
          <a href="/app/dashboard" className="mt-6 inline-flex h-11 items-center justify-center rounded-xl bg-primary px-6 text-sm font-medium text-primary-foreground hover:bg-primary/90">Criar campanha</a>
        </div>
      </section>
    </>
  );
}
