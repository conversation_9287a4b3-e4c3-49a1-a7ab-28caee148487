import { Hero } from "@/components/composite/Hero";
import { ToolsCarousel } from "@/components/composite/ToolsCarousel";
import { ValueGrid } from "@/components/composite/ValueGrid";

export const metadata = { title: "uTulz — Suas ferramentas para campanhas" };

export default function HomePage() {
  return (
    <>
      <Hero />
      <ValueGrid />
      <ToolsCarousel />
      <section id="testar" className="bg-white py-16">
        <div className="mx-auto w-full max-w-3xl px-4 text-center">
          <h2 className="text-2xl font-semibold text-slate-900">Pronto para testar</h2>
          <p className="mt-2 text-slate-600">Crie uma campanha de exemplo e veja a mágica acontecer.</p>
          <a href="/app/dashboard" className="mt-6 inline-flex h-11 items-center justify-center rounded-xl bg-teal-500 px-6 text-sm font-medium text-white hover:bg-teal-600"><PERSON><PERSON><PERSON> campan<PERSON></a>
        </div>
      </section>
    </>
  );
}
