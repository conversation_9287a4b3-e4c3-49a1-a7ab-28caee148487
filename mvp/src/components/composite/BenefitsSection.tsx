import { Badge } from "@/components/ui/Badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/Card";
import { Container } from "@/components/ui/Container";
import { 
  Clock, 
  DollarSign, 
  Shield, 
  TrendingUp, 
  Users, 
  Zap 
} from "lucide-react";

const benefits = [
  {
    icon: Clock,
    title: "Economia de Tempo",
    description: "Reduza em até 80% o tempo gasto em tarefas repetitivas e foque no que realmente importa: estratégia e criatividade.",
    metric: "80%",
    metricLabel: "menos tempo"
  },
  {
    icon: DollarSign,
    title: "Redução de Custos",
    description: "Diminua custos operacionais significativamente ao automatizar processos manuais e otimizar recursos.",
    metric: "60%",
    metricLabel: "economia"
  },
  {
    icon: TrendingUp,
    title: "Melhores Resultados",
    description: "Campanhas mais efetivas com insights baseados em dados e otimização contínua por IA.",
    metric: "3x",
    metricLabel: "ROI médio"
  },
  {
    icon: Users,
    title: "Escalabilidade",
    description: "Atenda mais clientes sem aumentar proporcionalmente sua equipe, mantendo a qualidade do serviço.",
    metric: "5x",
    metricLabel: "mais clientes"
  },
  {
    icon: Shield,
    title: "Consistência",
    description: "Garanta entregas consistentes e de alta qualidade, independente da complexidade do projeto.",
    metric: "99%",
    metricLabel: "precisão"
  },
  {
    icon: Zap,
    title: "Agilidade",
    description: "Responda rapidamente às demandas do mercado com processos otimizados e automatizados.",
    metric: "10x",
    metricLabel: "mais rápido"
  }
];

export function BenefitsSection() {
  return (
    <section id="benefits" className="py-20 md:py-32 bg-muted/30">
      <Container>
        {/* Header */}
        <div className="text-center space-y-4 mb-16">
          <Badge variant="secondary" className="w-fit mx-auto">
            <TrendingUp className="w-3 h-3 mr-2" />
            Benefícios Comprovados
          </Badge>
          
          <h2 className="text-3xl md:text-4xl lg:text-5xl tracking-tight">
            Transforme sua{" "}
            <span className="text-primary">operação publicitária</span>
          </h2>
          
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Veja como a uTulz pode revolucionar a forma como sua agência trabalha, 
            entregando resultados superiores com menos esforço.
          </p>
        </div>

        {/* Benefits Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {benefits.map((benefit, index) => (
            <Card key={index} className="group hover:shadow-lg transition-all duration-300">
              <CardHeader className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="p-3 rounded-xl bg-primary/10">
                    <benefit.icon className="w-6 h-6 text-primary" />
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-primary">{benefit.metric}</div>
                    <div className="text-xs text-muted-foreground">{benefit.metricLabel}</div>
                  </div>
                </div>
                
                <CardTitle className="text-xl">{benefit.title}</CardTitle>
              </CardHeader>
              
              <CardContent>
                <p className="text-muted-foreground">{benefit.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="inline-flex items-center space-x-2 text-sm text-muted-foreground">
            <Shield className="w-4 h-4" />
            <span>Resultados baseados em dados reais de nossos clientes</span>
          </div>
        </div>
      </Container>
    </section>
  );
}
