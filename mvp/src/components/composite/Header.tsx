"use client";
import { Logo } from "@/components/branding/Logo";
import { Button } from "@/components/ui/Button";
import { Container } from "@/components/ui/Container";
import Link from "next/link";
import { useState } from "react";
import { AuthDialog } from "./auth/AuthDialog";

export function Header() {
    const [open, setOpen] = useState(false);
    return (
        <header className="sticky top-0 z-50 border-b border-slate-200/60 bg-white/80 backdrop-blur supports-[backdrop-filter]:bg-white/60">
            <Container className="flex h-16 items-center justify-between">
                <Link href="/" className="flex items-center gap-8">
                    <Logo />
                    <nav className="hidden items-center gap-6 text-sm text-slate-700 md:flex">
                        <Link href="/#ferramentas" className="hover:text-slate-900">Ferramentas</Link>
                        <Link href="/preco" className="hover:text-slate-900">Preço</Link>
                        <Link href="/campanha" className="hover:text-slate-900">Campanha</Link>
                    </nav>
                </Link>
                <div className="flex items-center gap-3">
                    <Button variant="ghost" onClick={() => setOpen(true)}>Entrar</Button>
                    <Button onClick={() => setOpen(true)}>Criar conta</Button>
                </div>
            </Container>
            <AuthDialog open={open} onClose={() => setOpen(false)} />
        </header>
    );
}
