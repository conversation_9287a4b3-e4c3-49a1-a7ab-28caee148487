"use client";
import { useEffect } from "react";

export function AuthDialog({ open, onClose }: { open: boolean; onClose: () => void }) {
    useEffect(() => {
        function onEsc(e: KeyboardEvent) { if (e.key === "Escape") onClose(); }
        if (open) document.addEventListener("keydown", onEsc);
        return () => document.removeEventListener("keydown", onEsc);
    }, [open, onClose]);

    if (!open) return null;
    return (
        <div aria-modal className="fixed inset-0 z-50 grid place-items-center bg-slate-900/30 p-4" onClick={onClose}>
            <div className="w-full max-w-md rounded-2xl border border-slate-200 bg-white p-6 shadow-xl" onClick={(e) => e.stopPropagation()}>
                <h2 className="text-lg font-semibold text-slate-900">Entrar ou criar conta</h2>
                <p className="mt-1 text-sm text-slate-600">Use email e senha ou sua conta Google.</p>
                <form className="mt-4 space-y-3">
                    <div className="space-y-1">
                        <label className="text-sm text-slate-700">Email</label>
                        <input className="h-11 w-full rounded-xl border border-slate-300 px-3 outline-none ring-teal-600 focus:ring-2" type="email" />
                    </div>
                    <div className="space-y-1">
                        <label className="text-sm text-slate-700">Senha</label>
                        <input className="h-11 w-full rounded-xl border border-slate-300 px-3 outline-none ring-teal-600 focus:ring-2" type="password" />
                    </div>
                    <div className="space-y-1">
                        <label className="text-sm text-slate-700">Área principal (opcional)</label>
                        <input className="h-11 w-full rounded-xl border border-slate-300 px-3 outline-none ring-teal-600 focus:ring-2" placeholder="Atendimento, Mídia, Criação..." />
                    </div>
                    <div className="flex gap-2 pt-2">
                        <button className="h-11 flex-1 rounded-xl bg-teal-500 text-white hover:bg-teal-600">Enviar</button>
                        <button className="h-11 flex-1 rounded-xl border border-slate-300 bg-white hover:bg-slate-50">Entrar com Google</button>
                    </div>
                </form>
                <button className="mt-4 w-full rounded-xl bg-slate-100 py-2 text-sm text-slate-700 hover:bg-slate-200" onClick={onClose}>Fechar</button>
            </div>
        </div>
    );
}
