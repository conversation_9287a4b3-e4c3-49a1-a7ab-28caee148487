"use client";
import { Button } from "@/components/ui/Button";
import { Card } from "@/components/ui/Card";
import { Container } from "@/components/ui/Container";
import { useMemo, useState } from "react";

type Tool = { name: string; area: string; type: string; mvp?: boolean; desc: string };

const tools: Tool[] = [
    { name: "Briefing inicial", area: "Atendimento", type: "Docs", mvp: true, desc: "Coleta estruturada e documento final." },
    { name: "Plano de mídia", area: "Mídia", type: "Sheets", mvp: true, desc: "Canais, formatos e estimativas." },
    { name: "Conceito criativo e KV", area: "Criação", type: "Docs + Imagem", mvp: true, desc: "Mensagem central e diretrizes visuais." },
    { name: "Peça visual final", area: "Produção", type: "Imagem", mvp: true, desc: "KV final em alta." },
    { name: "Publicação/Acompanhamento", area: "Publicação", type: "Docs", mvp: true, desc: "Status de veiculação e andamentos." },
    { name: "Calendário editorial", area: "Social", type: "Sheets", mvp: true, desc: "Planejamento de posts." },
    { name: "Setup de tracking", area: "BI", type: "Docs", mvp: true, desc: "Tags e eventos essenciais." },
    { name: "Criador de Apresentações", area: "Conteúdo", type: "Slides", mvp: true, desc: "Transforma conteúdo em deck." }
];

export function ToolsCarousel() {
    const [onlyMvp, setOnlyMvp] = useState(true);
    const list = useMemo(() => (onlyMvp ? tools.filter(t => t.mvp) : tools), [onlyMvp]);

    return (
        <section className="py-16" id="ferramentas">
            <Container>
                <div className="mb-6 flex items-center justify-between">
                    <h2 className="text-xl font-semibold text-slate-900">Ferramentas</h2>
                    <label className="flex items-center gap-2 text-sm text-slate-600">
                        <input type="checkbox" checked={onlyMvp} onChange={() => setOnlyMvp(v => !v)} className="h-4 w-4 accent-teal-600" />
                        Mostrar só MVP
                    </label>
                </div>
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    {list.map((t) => (
                        <Card key={t.name}>
                            <div className="flex items-start justify-between gap-6">
                                <div>
                                    <h3 className="text-base font-semibold text-slate-900">{t.name}</h3>
                                    <p className="mt-1 text-xs text-slate-500">{t.area} • {t.type}</p>
                                </div>
                                {t.mvp && <span className="rounded-md bg-teal-50 px-2 py-1 text-xs font-medium text-teal-700 ring-1 ring-inset ring-teal-200">MVP</span>}
                            </div>
                            <p className="mt-3 text-sm text-slate-600">{t.desc}</p>
                            <div className="mt-4 flex gap-2">
                                <Button className="h-9 px-3">Usar agora</Button>
                                <Button variant="ghost" className="h-9 px-3">Ver detalhes</Button>
                            </div>
                        </Card>
                    ))}
                </div>
            </Container>
        </section>
    );
}
