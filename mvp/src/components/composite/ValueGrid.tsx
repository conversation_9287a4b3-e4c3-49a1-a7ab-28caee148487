import { Card } from "@/components/ui/Card";
import { Container } from "@/components/ui/Container";

const items = [
    { title: "Briefing sem novela", desc: "Perguntas certas. Documento limpo. Alinhamento rápido." },
    { title: "Plano de mídia que se explica", desc: "Cenários, estimativas e justificativas em uma planilha clara." },
    { title: "Relatório que ensina algo", desc: "Leitura objetiva, insights acionáveis e próximos passos." }
];

export function ValueGrid() {
    return (
        <section className="bg-slate-50/60 py-16" id="valor">
            <Container>
                <div className="grid gap-6 md:grid-cols-3">
                    {items.map((it) => (
                        <Card key={it.title} className="h-full">
                            <h3 className="text-base font-semibold text-slate-900">{it.title}</h3>
                            <p className="mt-2 text-sm text-slate-600">{it.desc}</p>
                        </Card>
                    ))}
                </div>
            </Container>
        </section>
    );
}
