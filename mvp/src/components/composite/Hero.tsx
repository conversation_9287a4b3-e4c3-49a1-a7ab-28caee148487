import { Badge } from "@/components/ui/Badge";
import { But<PERSON> } from "@/components/ui/Button";
import { Container } from "@/components/ui/Container";
import Image from "next/image";
import Link from "next/link";

export function Hero() {
    return (
        <section className="relative overflow-hidden">
            <div className="pointer-events-none absolute inset-0 -z-10 bg-gradient-to-b from-slate-50 via-white to-white" />
            <Container className="grid items-center gap-10 py-16 md:grid-cols-2 md:py-24">
                <div className="space-y-6">
                    <Badge>Para agências e profissionais</Badge>
                    <h1 className="text-balance text-4xl font-semibold tracking-tight text-slate-900 md:text-5xl">
                        Operação publicitária sem ruído. Resultados com consistência.
                    </h1>
                    <p className="text-pretty text-base text-slate-600 md:text-lg">
                        uTulz reúne agentes prontos para briefing, plano de mídia, relatórios e apresentação. Menos incêndio, mais entrega.
                    </p>
                    <div className="flex flex-wrap gap-3">
                        <Link href="/#testar"><Button>Testar agora</Button></Link>
                        <Link href="/preco"><Button variant="ghost">Ver planos</Button></Link>
                    </div>
                    <ul className="mt-4 grid gap-2 text-sm text-slate-600">
                        <li>• Acessibilidade AA</li>
                        <li>• Login com Google</li>
                        <li>• Integração Docs, Sheets e Slides</li>
                    </ul>
                </div>
                <div className="relative">
                    <div className="relative overflow-hidden rounded-3xl border border-slate-200/70 bg-white shadow-sm">
                        <Image
                            src="https://images.unsplash.com/photo-1515378791036-0648a3ef77b2?q=80&w=1600&auto=format&fit=crop"
                            alt="Exemplo de campanha"
                            width={1200}
                            height={900}
                            className="h-auto w-full"
                            priority
                        />
                    </div>
                    <div className="pointer-events-none absolute -left-10 -top-10 h-40 w-40 rounded-full bg-teal-100/70 blur-3xl" />
                    <div className="pointer-events-none absolute -bottom-6 -right-6 h-32 w-32 rounded-full bg-indigo-100/60 blur-3xl" />
                </div>
            </Container>
        </section>
    );
}
