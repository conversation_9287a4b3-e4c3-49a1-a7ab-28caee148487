type Props = React.ButtonHTMLAttributes<HTMLButtonElement> & { variant?: "primary" | "ghost"; asChild?: boolean };
export function Button({ variant = "primary", className = "", ...props }: Props) {
    const base = "inline-flex items-center justify-center rounded-xl text-sm font-medium transition focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2";
    const sizes = "h-11 px-5";
    const styles =
        variant === "primary"
            ? "bg-teal-500 text-white hover:bg-teal-600 focus-visible:ring-teal-600"
            : "bg-transparent text-slate-900 hover:bg-slate-100 focus-visible:ring-slate-400";
    return <button className={`${base} ${sizes} ${styles} ${className}`} {...props} />;
}
